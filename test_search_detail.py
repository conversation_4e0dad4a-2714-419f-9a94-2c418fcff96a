#!/usr/bin/env python3
"""
搜索结果详情功能测试脚本
测试用户点击搜索结果条目进入详情界面的完整流程
"""

import os
import sys
import json
from typing import Dict, Any, List
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langchain_core.documents import Document
from app.core.retrievers import create_ensemble_retriever
from app.core.vector_store import get_vector_store

class SearchDetailService:
    """搜索结果详情服务 - 模拟后端API服务"""
    
    def __init__(self):
        self.retriever = None
        self.search_results_cache = {}  # 缓存搜索结果
        
    def initialize_retriever(self):
        """初始化混合检索器"""
        print("🔧 初始化混合检索器...")
        
        # 配置搜索引擎
        search_config = {
            'type': 'simple',
            'simple': {
                'index_name': 'medical_detail_test',
                'persist_directory': './data/search_detail_index'
            }
        }
        
        # 配置重排器（禁用以简化测试）
        reranker_config = {
            'type': 'bge_reranker',
            'model': {'name': 'BAAI/bge-reranker-base', 'device': 'cpu'},
            'parameters': {'top_n': 10, 'batch_size': 16}
        }
        
        self.retriever = create_ensemble_retriever(
            vector_weight=0.6,
            keyword_weight=0.4,
            rrf_k=60,
            top_k=10,
            search_engine_config=search_config,
            reranker_config=reranker_config,
            enable_reranking=False  # 禁用重排器以简化测试
        )
        
        print("✅ 混合检索器初始化完成")
    
    def hybrid_search(self, query: str, user_id: str = "test_user") -> Dict[str, Any]:
        """执行混合搜索 - 模拟用户搜索请求"""
        print(f"\n🔍 执行混合搜索: '{query}'")
        
        try:
            # 执行检索
            results = self.retriever.invoke(query)
            
            # 处理搜索结果，添加必要的元数据
            search_results = []
            for i, doc in enumerate(results[:5]):  # 限制返回5个结果
                doc_id = f"doc_{hash(doc.page_content[:50])}_{i}"
                
                # 构建搜索结果条目
                result_item = {
                    'doc_id': doc_id,
                    'title': self._extract_title(doc),
                    'summary': doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                    'source': doc.metadata.get('source', 'unknown'),
                    'category': doc.metadata.get('category', '医学知识'),
                    'rrf_score': doc.metadata.get('rrf_score', 0.0),
                    'relevance_score': round(0.95 - i * 0.1, 2),  # 模拟相关性评分
                    'timestamp': datetime.now().isoformat()
                }
                
                search_results.append(result_item)
                
                # 缓存完整文档信息用于详情查询
                self.search_results_cache[doc_id] = {
                    'document': doc,
                    'result_item': result_item,
                    'full_metadata': doc.metadata
                }
            
            response = {
                'query': query,
                'user_id': user_id,
                'total_results': len(search_results),
                'results': search_results,
                'search_time': f"{len(search_results) * 0.1:.2f}s",  # 模拟搜索时间
                'fusion_method': 'RRF',
                'weights': {'vector': 0.6, 'keyword': 0.4}
            }
            
            print(f"✅ 搜索完成，返回 {len(search_results)} 个结果")
            return response
            
        except Exception as e:
            print(f"❌ 搜索失败: {str(e)}")
            return {
                'error': f'搜索失败: {str(e)}',
                'query': query,
                'results': []
            }
    
    def get_document_detail(self, doc_id: str, user_id: str = "test_user") -> Dict[str, Any]:
        """获取文档详情 - 模拟用户点击搜索结果条目"""
        print(f"\n📄 获取文档详情: {doc_id}")
        
        try:
            # 从缓存中获取文档信息
            if doc_id not in self.search_results_cache:
                return {
                    'error': 'Document not found',
                    'doc_id': doc_id,
                    'message': '文档不存在或已过期，请重新搜索'
                }
            
            cached_data = self.search_results_cache[doc_id]
            document = cached_data['document']
            result_item = cached_data['result_item']
            full_metadata = cached_data['full_metadata']
            
            # 构建详情响应
            detail_response = {
                # 基本信息
                'doc_id': doc_id,
                'title': result_item['title'],
                'category': result_item['category'],
                'source': result_item['source'],
                
                # 完整内容
                'content': document.page_content,
                'content_length': len(document.page_content),
                'content_preview': document.page_content[:500] + "..." if len(document.page_content) > 500 else document.page_content,
                
                # 评分信息
                'relevance_score': result_item['relevance_score'],
                'rrf_score': result_item['rrf_score'],
                'confidence_level': self._calculate_confidence(result_item['relevance_score']),
                
                # 数据源信息
                'data_source': {
                    'type': full_metadata.get('source_type', 'document'),
                    'file_path': full_metadata.get('source', ''),
                    'page_number': full_metadata.get('page', None),
                    'chunk_id': full_metadata.get('chunk_id', None),
                    'extraction_time': full_metadata.get('extraction_time', None)
                },
                
                # 融合信息
                'fusion_info': {
                    'rrf_score': result_item['rrf_score'],
                    'fusion_sources': full_metadata.get('fusion_sources', ['vector']),
                    'vector_weight': 0.6,
                    'keyword_weight': 0.4,
                    'rank_in_vector': full_metadata.get('vector_rank', None),
                    'rank_in_keyword': full_metadata.get('keyword_rank', None)
                },
                
                # 元数据
                'metadata': {
                    key: value for key, value in full_metadata.items()
                    if isinstance(value, (str, int, float, bool)) or value is None
                },
                
                # 相关建议
                'related_suggestions': self._generate_suggestions(document.page_content),
                
                # 访问信息
                'access_info': {
                    'user_id': user_id,
                    'access_time': datetime.now().isoformat(),
                    'view_count': 1  # 实际应用中应该是数据库计数
                }
            }
            
            print(f"✅ 文档详情获取成功")
            print(f"   - 标题: {detail_response['title']}")
            print(f"   - 内容长度: {detail_response['content_length']} 字符")
            print(f"   - 相关性评分: {detail_response['relevance_score']}")
            print(f"   - RRF融合分数: {detail_response['rrf_score']}")
            
            return detail_response
            
        except Exception as e:
            print(f"❌ 获取文档详情失败: {str(e)}")
            return {
                'error': f'获取文档详情失败: {str(e)}',
                'doc_id': doc_id
            }
    
    def _extract_title(self, doc: Document) -> str:
        """从文档中提取标题"""
        # 优先使用元数据中的标题
        if 'title' in doc.metadata:
            return doc.metadata['title']
        
        # 从内容中提取第一行作为标题
        lines = doc.page_content.split('\n')
        for line in lines:
            line = line.strip()
            if line and len(line) < 100:
                return line
        
        # 使用内容的前50个字符作为标题
        return doc.page_content[:50] + "..."
    
    def _calculate_confidence(self, relevance_score: float) -> str:
        """计算置信度等级"""
        if relevance_score >= 0.9:
            return "高置信度"
        elif relevance_score >= 0.7:
            return "中等置信度"
        else:
            return "低置信度"
    
    def _generate_suggestions(self, content: str) -> List[str]:
        """生成相关建议"""
        # 简单的关键词提取生成建议
        keywords = ['头痛', '发热', '症状', '治疗', '诊断', '药物']
        suggestions = []
        
        for keyword in keywords:
            if keyword in content:
                suggestions.append(f"了解更多关于{keyword}的信息")
        
        if not suggestions:
            suggestions = ["查看相关医学资料", "咨询专业医生"]
        
        return suggestions[:3]  # 最多返回3个建议


def test_search_and_detail_workflow():
    """测试完整的搜索和详情查看工作流程"""
    print("🚀 开始测试搜索结果详情功能")
    print("=" * 60)
    
    # 初始化服务
    service = SearchDetailService()
    service.initialize_retriever()
    
    # 测试查询
    test_queries = [
        "头痛的症状和治疗方法",
        "发热的原因和处理",
        "高血压的诊断标准"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 测试场景 {i}: {query}")
        print("-" * 40)
        
        # 步骤1: 执行搜索
        search_response = service.hybrid_search(query, f"user_{i}")
        
        if 'error' in search_response:
            print(f"❌ 搜索测试失败: {search_response['error']}")
            continue
        
        print(f"🔍 搜索结果摘要:")
        print(f"   - 查询词: {search_response['query']}")
        print(f"   - 结果数量: {search_response['total_results']}")
        print(f"   - 搜索时间: {search_response['search_time']}")
        print(f"   - 融合方法: {search_response['fusion_method']}")
        
        # 显示搜索结果列表
        print(f"\n📋 搜索结果列表:")
        for j, result in enumerate(search_response['results'], 1):
            print(f"   {j}. {result['title']}")
            print(f"      相关性: {result['relevance_score']} | RRF分数: {result['rrf_score']:.4f}")
            print(f"      摘要: {result['summary'][:100]}...")
        
        # 步骤2: 模拟用户点击第一个结果
        if search_response['results']:
            first_result = search_response['results'][0]
            doc_id = first_result['doc_id']
            
            print(f"\n👆 用户点击: {first_result['title']}")
            
            # 获取详细信息
            detail_response = service.get_document_detail(doc_id, f"user_{i}")
            
            if 'error' in detail_response:
                print(f"❌ 详情获取失败: {detail_response['error']}")
                continue
            
            # 显示详细信息
            print(f"\n📄 文档详情:")
            print(f"   标题: {detail_response['title']}")
            print(f"   类别: {detail_response['category']}")
            print(f"   数据源: {detail_response['source']}")
            print(f"   内容长度: {detail_response['content_length']} 字符")
            print(f"   相关性评分: {detail_response['relevance_score']}")
            print(f"   RRF融合分数: {detail_response['rrf_score']:.4f}")
            print(f"   置信度: {detail_response['confidence_level']}")
            
            print(f"\n🔗 数据源信息:")
            ds_info = detail_response['data_source']
            print(f"   类型: {ds_info['type']}")
            print(f"   文件路径: {ds_info['file_path']}")
            if ds_info['page_number']:
                print(f"   页码: {ds_info['page_number']}")
            
            print(f"\n🔄 融合信息:")
            fusion_info = detail_response['fusion_info']
            print(f"   融合来源: {', '.join(fusion_info['fusion_sources'])}")
            print(f"   向量权重: {fusion_info['vector_weight']}")
            print(f"   关键词权重: {fusion_info['keyword_weight']}")
            
            print(f"\n💡 相关建议:")
            for suggestion in detail_response['related_suggestions']:
                print(f"   - {suggestion}")
            
            print(f"\n📊 内容预览:")
            print(f"   {detail_response['content_preview']}")
            
        print(f"\n✅ 测试场景 {i} 完成")
    
    print("\n" + "=" * 60)
    print("🎉 搜索结果详情功能测试完成！")
    
    # 生成测试报告
    generate_test_report(service)


def generate_test_report(service: SearchDetailService):
    """生成测试报告"""
    print(f"\n📊 测试报告")
    print("-" * 30)
    
    cached_docs = len(service.search_results_cache)
    print(f"缓存文档数量: {cached_docs}")
    print(f"测试场景: 3个")
    print(f"功能验证: ✅ 搜索功能")
    print(f"           ✅ 详情获取")
    print(f"           ✅ 元数据处理")
    print(f"           ✅ 评分计算")
    print(f"           ✅ 相关建议")
    
    # 模拟API响应格式
    print(f"\n🔧 API响应格式验证:")
    if cached_docs > 0:
        sample_doc_id = list(service.search_results_cache.keys())[0]
        sample_detail = service.get_document_detail(sample_doc_id)
        
        required_fields = [
            'doc_id', 'title', 'content', 'relevance_score', 
            'rrf_score', 'data_source', 'fusion_info', 'metadata'
        ]
        
        missing_fields = [field for field in required_fields if field not in sample_detail]
        
        if not missing_fields:
            print("   ✅ 所有必需字段都存在")
        else:
            print(f"   ❌ 缺失字段: {', '.join(missing_fields)}")


def simulate_api_endpoints():
    """模拟API端点测试"""
    print(f"\n🌐 API端点模拟测试")
    print("-" * 30)
    
    service = SearchDetailService()
    service.initialize_retriever()
    
    # 模拟 POST /api/hybrid-search
    print("📡 POST /api/hybrid-search")
    request_data = {
        "query": "头痛治疗方法",
        "user_id": "test_api_user",
        "top_k": 5
    }
    
    search_result = service.hybrid_search(request_data["query"], request_data["user_id"])
    print(f"   响应状态: {'成功' if 'results' in search_result else '失败'}")
    print(f"   返回结果数: {len(search_result.get('results', []))}")
    
    # 模拟 POST /api/search-detail  
    if search_result.get('results'):
        print("\n📡 POST /api/search-detail")
        detail_request = {
            "doc_id": search_result['results'][0]['doc_id'],
            "user_id": "test_api_user"
        }
        
        detail_result = service.get_document_detail(detail_request["doc_id"], detail_request["user_id"])
        print(f"   响应状态: {'成功' if 'content' in detail_result else '失败'}")
        print(f"   内容长度: {detail_result.get('content_length', 0)} 字符")
        print(f"   相关性评分: {detail_result.get('relevance_score', 0)}")
        
        # 保存示例响应到文件
        with open('sample_api_responses.json', 'w', encoding='utf-8') as f:
            sample_responses = {
                'search_response': search_result,
                'detail_response': detail_result
            }
            json.dump(sample_responses, f, ensure_ascii=False, indent=2)
        
        print(f"   📁 示例响应已保存到: sample_api_responses.json")


if __name__ == "__main__":
    try:
        # 执行主要测试
        test_search_and_detail_workflow()
        
        # 执行API模拟测试
        simulate_api_endpoints()
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试执行异常: {str(e)}")
        import traceback
        traceback.print_exc() 