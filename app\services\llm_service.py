import requests
import logging
import json
from flask import current_app

logger = logging.getLogger(__name__)

class LLMService:
    """
    Service for integrating with LLM APIs
    """
    
    def __init__(self, config=None):
        """
        Initialize the LLM service
        
        Args:
            config: Configuration object (if None, will use current_app.config)
        """
        self.config = config
        
    def _get_config(self):
        """Get config from current_app if not provided at initialization"""
        if self.config:
            return self.config

        # Try to get from current_app, fallback to environment variables
        try:
            return current_app.config
        except RuntimeError:
            # Working outside of application context, use environment variables
            import os
            return {
                'LLM_API_URL': os.getenv('LLM_API_URL', 'http://huatuo.fucku.top/chat/completions'),
                'LLM_MODEL_NAME': os.getenv('LLM_MODEL_NAME', 'huatuogpt-7b'),
                'MAX_TOKENS': int(os.getenv('MAX_TOKENS', '512')),
                'RESPONSE_TEMPERATURE': float(os.getenv('RESPONSE_TEMPERATURE', '0.7')),
                'LLM_API_KEY': os.getenv('LLM_API_KEY', '')
            }
        
    def generate_response(self, message, history=None, stream=False):
        """
        Generate a response from the LLM with multi-turn conversation support

        Args:
            message (str): User message
            history (list, optional): List of previous messages in format:
                [{"role": "user", "content": "..."}, {"role": "bot", "content": "..."}]
            stream (bool): Whether to use streaming response

        Returns:
            str or generator: Generated response (string for non-stream, generator for stream)
        """
        config = self._get_config()
        
        # Check if API URL is configured
        api_url = config.get('LLM_API_URL')
        if not api_url:
            logger.warning("LLM API URL not configured, using fallback")
            return self._generate_fallback(message)

        model_name = config.get('LLM_MODEL_NAME')
        max_tokens = config.get('MAX_TOKENS', 512)
        temperature = config.get('RESPONSE_TEMPERATURE', 0.7)
        api_key = config.get('LLM_API_KEY')
        
        # 构建多轮对话消息列表
        messages = self._build_conversation_messages(message, history)

        # 添加调试日志，查看构建的消息
        logger.info(f"Built messages for LLM API: {json.dumps(messages, ensure_ascii=False, indent=2)}")

        try:
            # Prepare request data
            data = {
                "model": model_name,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream
            }

            # Send request to LLM API
            headers = {
                "Content-Type": "application/json"
            }

            # Add Authorization header only if API key is provided
            if api_key:
                headers["Authorization"] = f"Bearer {api_key}"
            
            logger.info(f"Sending request to LLM API: {api_url}")

            if stream:
                # Stream response
                response = requests.post(
                    api_url,
                    headers=headers,
                    data=json.dumps(data),
                    timeout=30,
                    stream=True
                )
                response.raise_for_status()
                return self._handle_stream_response(response)
            else:
                # Non-stream response
                response = requests.post(
                    api_url,
                    headers=headers,
                    data=json.dumps(data),
                    timeout=30
                )
                response.raise_for_status()
                result = response.json()

                # Extract generated text
                generated_text = result["choices"][0]["message"]["content"]
                return generated_text.strip()
            
        except Exception as e:
            logger.error(f"Error calling LLM API: {str(e)}")
            return self._generate_fallback(message)

    def _handle_stream_response(self, response):
        """
        Handle streaming response from LLM API

        Args:
            response: requests.Response object with stream=True

        Yields:
            str: Chunks of generated text
        """
        try:
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]  # Remove 'data: ' prefix
                        if data_str.strip() == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    yield delta['content']
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            logger.error(f"Error handling stream response: {str(e)}")
            yield self._generate_fallback("流式响应处理出错")

    def _build_conversation_messages(self, current_message, history=None):
        """
        构建多轮对话的消息列表

        Args:
            current_message (str): 当前用户消息
            history (list, optional): 历史对话记录

        Returns:
            list: 格式化的消息列表，用于发送给LLM API
        """
        messages = []

        # 系统提示词内容
        system_prompt = ("你是一个专业的医疗咨询助手，名为'慧问医答'。你的职责是帮助用户了解症状、提供初步诊断建议，并在必要时建议他们就医。请注意：\n"
                        "1. 不要确诊疾病，而是提供可能的原因和建议\n"
                        "2. 回答要专业、简洁、易懂\n"
                        "3. 严重症状时建议及时就医\n"
                        "4. 使用中文回复\n"
                        "5. 结合用户的历史对话提供连贯的建议\n\n")

        # 1. 尝试添加系统提示词（某些API可能不支持）
        messages.append({
            "role": "system",
            "content": system_prompt.strip()
        })

        # 2. 添加历史对话（如果有的话）
        if history:
            for item in history:
                # 角色映射：数据库中的'bot'映射为API需要的'assistant'
                role = item.get("role", "user")
                if role == "bot":
                    role = "assistant"

                messages.append({
                    "role": role,
                    "content": item.get("content", "")
                })

        # 3. 添加当前用户消息，如果是第一条消息且没有历史记录，则在消息前加上角色设定
        if not history or len(history) == 0:
            # 第一条消息，在用户消息前加上角色设定以确保生效
            enhanced_message = system_prompt + "用户问题：" + current_message
            messages.append({
                "role": "user",
                "content": enhanced_message
            })
        else:
            # 有历史记录，直接添加用户消息
            messages.append({
                "role": "user",
                "content": current_message
            })

        return messages

    def generate_response_stream(self, message, history=None):
        """
        Generate a streaming response from the LLM

        Args:
            message (str): User message
            history (list, optional): List of previous messages

        Yields:
            str: Chunks of generated text
        """
        for chunk in self.generate_response(message, history, stream=True):
            yield chunk

    def _generate_fallback(self, message):
        """
        Generate a fallback response when the LLM API is unavailable
        
        Args:
            message (str): User message
            
        Returns:
            str: Fallback response
        """
        # Simple keyword-based fallback
        message = message.lower()
        
        if '症状' in message:
            return "请描述一下您的具体症状，例如：发热持续了多久？是否伴有其他不适？"
        elif '头痛' in message:
            return "头痛可能由多种因素引起，如压力、疲劳、脱水、感冒或更严重的问题。如果头痛严重或持续存在，建议及时就医。请问您的头痛是持续性的还是间歇性的？"
        elif '发热' in message or '发烧' in message:
            return "发热是机体对感染或炎症的一种防御反应。请问您的体温是多少？是否伴有其他症状如咳嗽、喉咙痛或身体疼痛？"
        elif '谢谢' in message:
            return "不客气，很高兴能帮到您。如果您还有其他健康问题，随时可以咨询我。祝您健康！"
        else:
            return "感谢您的咨询。作为医疗咨询助手，我需要了解更多关于您的情况。请问您具体有什么症状或健康问题需要了解？" 