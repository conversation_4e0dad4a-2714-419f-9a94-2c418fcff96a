#!/usr/bin/env python3
"""
重排器功能测试脚本
测试SRH-002可插拔结果重排功能的完整流程
"""

import os
import sys
import json
import time
from typing import Dict, Any, List, Tuple
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langchain_core.documents import Document
from app.core.retrievers import create_ensemble_retriever
from app.core.rerankers import get_reranker
from app.core.vector_store import get_vector_store


class RerankerTestService:
    """重排器测试服务"""
    
    def __init__(self):
        self.retriever_with_rerank = None
        self.retriever_without_rerank = None
        self.standalone_reranker = None
        self.test_results = []
        
    def initialize_retrievers(self):
        """初始化带重排和不带重排的检索器"""
        print("🔧 初始化检索器...")
        
        # 搜索引擎配置
        search_config = {
            'type': 'simple',
            'simple': {
                'index_name': 'medical_rerank_test',
                'persist_directory': './data/rerank_test_index'
            }
        }
        
        # 重排器配置
        reranker_config = {
            'type': 'bge_reranker',
            'model': {
                'name': 'BAAI/bge-reranker-base', 
                'device': 'cpu',
                'max_length': 512,
                'normalize_scores': True
            },
            'parameters': {
                'top_n': 20,
                'batch_size': 16,
                'max_content_length': 512
            },
            'performance': {
                'use_cache': True
            }
        }
        
        # 创建带重排的检索器
        print("   创建带重排的检索器...")
        self.retriever_with_rerank = create_ensemble_retriever(
            vector_weight=0.6,
            keyword_weight=0.4,
            rrf_k=60,
            top_k=10,
            search_engine_config=search_config,
            reranker_config=reranker_config,
            enable_reranking=True
        )
        
        # 创建不带重排的检索器
        print("   创建不带重排的检索器...")
        self.retriever_without_rerank = create_ensemble_retriever(
            vector_weight=0.6,
            keyword_weight=0.4,
            rrf_k=60,
            top_k=10,
            search_engine_config=search_config,
            enable_reranking=False
        )
        
        # 创建独立的重排器用于单独测试
        print("   创建独立重排器...")
        self.standalone_reranker = get_reranker('bge_reranker', reranker_config)
        
        print("✅ 检索器初始化完成")
    
    def compare_search_results(self, query: str) -> Dict[str, Any]:
        """对比带重排和不带重排的搜索结果"""
        print(f"\n🔍 对比搜索测试: '{query}'")
        print("-" * 50)
        
        results = {
            'query': query,
            'timestamp': datetime.now().isoformat(),
            'without_rerank': {},
            'with_rerank': {},
            'comparison': {}
        }
        
        try:
            # 测试不带重排的检索
            print("📋 执行不带重排的检索...")
            start_time = time.time()
            docs_without_rerank = self.retriever_without_rerank.invoke(query)
            time_without_rerank = time.time() - start_time
            
            results['without_rerank'] = {
                'documents': self._format_documents(docs_without_rerank),
                'count': len(docs_without_rerank),
                'time': round(time_without_rerank, 3),
                'method': 'RRF融合'
            }
            
            print(f"   ✅ 完成，返回 {len(docs_without_rerank)} 个文档，耗时 {time_without_rerank:.3f}s")
            
            # 测试带重排的检索
            print("🔄 执行带重排的检索...")
            start_time = time.time()
            docs_with_rerank = self.retriever_with_rerank.invoke(query)
            time_with_rerank = time.time() - start_time
            
            results['with_rerank'] = {
                'documents': self._format_documents(docs_with_rerank),
                'count': len(docs_with_rerank),
                'time': round(time_with_rerank, 3),
                'method': 'RRF融合 + BGE重排'
            }
            
            print(f"   ✅ 完成，返回 {len(docs_with_rerank)} 个文档，耗时 {time_with_rerank:.3f}s")
            
            # 生成对比分析
            results['comparison'] = self._analyze_comparison(
                docs_without_rerank, docs_with_rerank, time_without_rerank, time_with_rerank
            )
            
            # 显示对比结果
            self._display_comparison_results(results)
            
            return results
            
        except Exception as e:
            print(f"❌ 对比测试失败: {str(e)}")
            results['error'] = str(e)
            return results
    
    def test_standalone_reranker(self, query: str, candidate_docs: List[Document]) -> Dict[str, Any]:
        """测试独立重排器功能"""
        print(f"\n🎯 独立重排器测试: '{query}'")
        print("-" * 50)
        
        if not candidate_docs:
            print("❌ 没有候选文档进行重排")
            return {'error': '没有候选文档'}
        
        try:
            print(f"📋 重排前文档数量: {len(candidate_docs)}")
            
            # 显示重排前的文档顺序
            print("🔢 重排前文档顺序:")
            for i, doc in enumerate(candidate_docs[:5], 1):
                rrf_score = doc.metadata.get('rrf_score', 0.0)
                title = self._extract_title(doc)
                print(f"   {i}. {title[:60]}... (RRF: {rrf_score:.4f})")
            
            # 执行重排
            print("\n🔄 执行重排...")
            start_time = time.time()
            reranked_docs = self.standalone_reranker.rerank(query, candidate_docs, top_n=10)
            rerank_time = time.time() - start_time
            
            print(f"✅ 重排完成，耗时 {rerank_time:.3f}s")
            
            # 显示重排后的文档顺序
            print("\n🎯 重排后文档顺序:")
            for i, doc in enumerate(reranked_docs[:5], 1):
                rerank_score = doc.metadata.get('rerank_score', 0.0)
                rrf_score = doc.metadata.get('rrf_score', 0.0)
                title = self._extract_title(doc)
                print(f"   {i}. {title[:60]}... (重排: {rerank_score:.4f}, RRF: {rrf_score:.4f})")
            
            # 分析重排效果
            ranking_changes = self._analyze_ranking_changes(candidate_docs, reranked_docs)
            
            result = {
                'query': query,
                'original_count': len(candidate_docs),
                'reranked_count': len(reranked_docs),
                'rerank_time': round(rerank_time, 3),
                'ranking_changes': ranking_changes,
                'reranked_documents': self._format_documents(reranked_docs),
                'model_info': self.standalone_reranker.get_model_info() if hasattr(self.standalone_reranker, 'get_model_info') else {}
            }
            
            print(f"\n📊 重排效果分析:")
            print(f"   排序变化文档数: {ranking_changes['changed_positions']}")
            print(f"   平均分数提升: {ranking_changes['avg_score_improvement']:.4f}")
            print(f"   最大排名变化: {ranking_changes['max_position_change']}")
            
            return result
            
        except Exception as e:
            print(f"❌ 独立重排器测试失败: {str(e)}")
            return {'error': str(e)}
    
    def batch_test_queries(self, test_queries: List[str]) -> List[Dict[str, Any]]:
        """批量测试多个查询"""
        print(f"\n🚀 批量测试开始，共 {len(test_queries)} 个查询")
        print("=" * 60)
        
        batch_results = []
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📋 测试 {i}/{len(test_queries)}: {query}")
            
            # 执行对比测试
            comparison_result = self.compare_search_results(query)
            batch_results.append(comparison_result)
            
            # 如果有不带重排的结果，用它们测试独立重排器
            if 'without_rerank' in comparison_result and comparison_result['without_rerank'].get('documents'):
                # 重新构建Document对象用于独立重排测试
                candidate_docs = []
                for doc_data in comparison_result['without_rerank']['documents'][:10]:
                    doc = Document(
                        page_content=doc_data['content'],
                        metadata=doc_data['metadata']
                    )
                    candidate_docs.append(doc)
                
                standalone_result = self.test_standalone_reranker(query, candidate_docs)
                comparison_result['standalone_rerank'] = standalone_result
            
            # 添加到测试结果记录
            self.test_results.append(comparison_result)
            
            print(f"✅ 测试 {i} 完成")
        
        return batch_results
    
    def _format_documents(self, documents: List[Document]) -> List[Dict[str, Any]]:
        """格式化文档为字典格式"""
        formatted_docs = []
        for i, doc in enumerate(documents):
            formatted_doc = {
                'rank': i + 1,
                'title': self._extract_title(doc),
                'content': doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                'content_length': len(doc.page_content),
                'metadata': {
                    'rrf_score': doc.metadata.get('rrf_score', 0.0),
                    'rerank_score': doc.metadata.get('rerank_score', 0.0),
                    'source': doc.metadata.get('source', 'unknown'),
                    'fusion_sources': doc.metadata.get('fusion_sources', [])
                }
            }
            formatted_docs.append(formatted_doc)
        return formatted_docs
    
    def _extract_title(self, doc: Document) -> str:
        """从文档中提取标题"""
        if 'title' in doc.metadata:
            return doc.metadata['title']
        
        lines = doc.page_content.split('\n')
        for line in lines:
            line = line.strip()
            if line and len(line) < 100:
                return line
        
        return doc.page_content[:50] + "..."
    
    def _analyze_comparison(self, docs_without: List[Document], docs_with: List[Document], 
                          time_without: float, time_with: float) -> Dict[str, Any]:
        """分析对比结果"""
        return {
            'time_difference': round(time_with - time_without, 3),
            'time_overhead_percent': round((time_with - time_without) / time_without * 100, 1) if time_without > 0 else 0,
            'document_count_change': len(docs_with) - len(docs_without),
            'ranking_similarity': self._calculate_ranking_similarity(docs_without, docs_with),
            'score_improvements': self._calculate_score_improvements(docs_without, docs_with)
        }
    
    def _analyze_ranking_changes(self, original_docs: List[Document], reranked_docs: List[Document]) -> Dict[str, Any]:
        """分析排序变化"""
        # 创建原始文档的位置映射
        original_positions = {}
        for i, doc in enumerate(original_docs):
            doc_key = self._get_document_key(doc)
            original_positions[doc_key] = i
        
        position_changes = []
        score_improvements = []
        
        for new_pos, doc in enumerate(reranked_docs):
            doc_key = self._get_document_key(doc)
            old_pos = original_positions.get(doc_key, -1)
            
            if old_pos != -1:
                position_change = old_pos - new_pos  # 正数表示排名提升
                position_changes.append(abs(position_change))
                
                # 计算分数改进
                old_score = doc.metadata.get('rrf_score', 0.0)
                new_score = doc.metadata.get('rerank_score', 0.0)
                if new_score > old_score:
                    score_improvements.append(new_score - old_score)
        
        return {
            'changed_positions': len([x for x in position_changes if x > 0]),
            'avg_position_change': round(sum(position_changes) / len(position_changes), 2) if position_changes else 0,
            'max_position_change': max(position_changes) if position_changes else 0,
            'avg_score_improvement': round(sum(score_improvements) / len(score_improvements), 4) if score_improvements else 0,
            'documents_with_score_improvement': len(score_improvements)
        }
    
    def _get_document_key(self, doc: Document) -> str:
        """生成文档的唯一标识符"""
        if 'chunk_id' in doc.metadata:
            return doc.metadata['chunk_id']
        elif '_id' in doc.metadata:
            return doc.metadata['_id']
        else:
            return str(hash(doc.page_content[:100]))
    
    def _calculate_ranking_similarity(self, docs1: List[Document], docs2: List[Document]) -> float:
        """计算排序相似度"""
        if not docs1 or not docs2:
            return 0.0
        
        # 简化的相似度计算：比较前5个文档的重叠度
        keys1 = [self._get_document_key(doc) for doc in docs1[:5]]
        keys2 = [self._get_document_key(doc) for doc in docs2[:5]]
        
        intersection = len(set(keys1) & set(keys2))
        union = len(set(keys1) | set(keys2))
        
        return round(intersection / union if union > 0 else 0.0, 3)
    
    def _calculate_score_improvements(self, docs_without: List[Document], docs_with: List[Document]) -> Dict[str, float]:
        """计算分数改进情况"""
        if not docs_without or not docs_with:
            return {'avg_improvement': 0.0, 'max_improvement': 0.0}
        
        # 比较前5个文档的平均分数
        avg_score_without = sum(doc.metadata.get('rrf_score', 0.0) for doc in docs_without[:5]) / min(5, len(docs_without))
        avg_score_with = sum(doc.metadata.get('rerank_score', 0.0) for doc in docs_with[:5]) / min(5, len(docs_with))
        
        return {
            'avg_improvement': round(avg_score_with - avg_score_without, 4),
            'max_improvement': round(max(doc.metadata.get('rerank_score', 0.0) for doc in docs_with[:5]) - 
                                   max(doc.metadata.get('rrf_score', 0.0) for doc in docs_without[:5]), 4)
        }
    
    def _display_comparison_results(self, results: Dict[str, Any]):
        """显示对比结果"""
        print(f"\n📊 对比结果分析:")
        
        without = results['without_rerank']
        with_rerank = results['with_rerank']
        comparison = results['comparison']
        
        print(f"   不带重排: {without['count']} 个文档, {without['time']}s")
        print(f"   带重排:   {with_rerank['count']} 个文档, {with_rerank['time']}s")
        print(f"   时间开销: +{comparison['time_difference']}s ({comparison['time_overhead_percent']}%)")
        print(f"   排序相似度: {comparison['ranking_similarity']}")
        print(f"   平均分数提升: {comparison['score_improvements']['avg_improvement']}")
        
        # 显示前3个结果的对比
        print(f"\n🔝 Top 3 结果对比:")
        print("   不带重排 vs 带重排")
        
        for i in range(min(3, len(without['documents']), len(with_rerank['documents']))):
            doc_without = without['documents'][i]
            doc_with = with_rerank['documents'][i]
            
            print(f"   {i+1}. {doc_without['title'][:40]}...")
            print(f"      vs {doc_with['title'][:40]}...")
            print(f"      分数: {doc_without['metadata']['rrf_score']:.4f} -> {doc_with['metadata']['rerank_score']:.4f}")


def main():
    """主测试函数"""
    print("🚀 重排器功能测试开始")
    print("=" * 60)
    
    # 初始化测试服务
    test_service = RerankerTestService()
    test_service.initialize_retrievers()
    
    # 定义测试查询
    test_queries = [
        "心肌梗死的症状和治疗方法",
        "高血压的诊断标准和用药指导", 
        "糖尿病患者的饮食注意事项",
        "头痛的常见原因和处理方式",
        "发热的病因分析和退热措施"
    ]
    
    try:
        # 执行批量测试
        batch_results = test_service.batch_test_queries(test_queries)
        
        # 生成测试报告
        generate_test_report(batch_results)
        
        # 保存测试结果
        save_test_results(batch_results)
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试执行异常: {str(e)}")
        import traceback
        traceback.print_exc()


def generate_test_report(results: List[Dict[str, Any]]):
    """生成测试报告"""
    print(f"\n📋 重排器测试报告")
    print("=" * 60)
    
    successful_tests = [r for r in results if 'error' not in r]
    failed_tests = [r for r in results if 'error' in r]
    
    print(f"测试总数: {len(results)}")
    print(f"成功: {len(successful_tests)}")
    print(f"失败: {len(failed_tests)}")
    
    if successful_tests:
        # 计算平均性能指标
        avg_time_overhead = sum(r['comparison']['time_overhead_percent'] for r in successful_tests) / len(successful_tests)
        avg_ranking_similarity = sum(r['comparison']['ranking_similarity'] for r in successful_tests) / len(successful_tests)
        avg_score_improvement = sum(r['comparison']['score_improvements']['avg_improvement'] for r in successful_tests) / len(successful_tests)
        
        print(f"\n📊 平均性能指标:")
        print(f"   时间开销: {avg_time_overhead:.1f}%")
        print(f"   排序相似度: {avg_ranking_similarity:.3f}")
        print(f"   分数提升: {avg_score_improvement:.4f}")
        
        print(f"\n✅ 重排器功能验证:")
        print(f"   ✅ 混合检索集成")
        print(f"   ✅ 独立重排器测试")
        print(f"   ✅ 性能对比分析")
        print(f"   ✅ 排序效果评估")
        print(f"   ✅ 批量查询处理")
    
    if failed_tests:
        print(f"\n❌ 失败的测试:")
        for i, test in enumerate(failed_tests, 1):
            print(f"   {i}. {test['query']}: {test['error']}")


def save_test_results(results: List[Dict[str, Any]]):
    """保存测试结果到文件"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"reranker_test_results_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n📁 测试结果已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 保存测试结果失败: {str(e)}")


def test_reranker_models():
    """测试不同重排器模型的效果"""
    print(f"\n🔬 重排器模型对比测试")
    print("=" * 60)

    models_to_test = [
        {
            'name': 'BGE Reranker Base',
            'config': {
                'type': 'bge_reranker',
                'model': {'name': 'BAAI/bge-reranker-base', 'device': 'cpu'},
                'parameters': {'top_n': 10, 'batch_size': 16}
            }
        },
        # 可以添加更多模型进行对比
    ]

    test_query = "心肌梗死的症状和治疗方法"

    # 先获取候选文档
    print("📋 获取候选文档...")
    test_service = RerankerTestService()
    test_service.initialize_retrievers()

    candidate_docs = test_service.retriever_without_rerank.invoke(test_query)
    print(f"   获得 {len(candidate_docs)} 个候选文档")

    model_results = []

    for model_info in models_to_test:
        print(f"\n🧪 测试模型: {model_info['name']}")

        try:
            # 创建重排器
            reranker = get_reranker(model_info['config']['type'], model_info['config'])

            # 执行重排
            start_time = time.time()
            reranked_docs = reranker.rerank(test_query, candidate_docs, top_n=10)
            rerank_time = time.time() - start_time

            # 记录结果
            result = {
                'model_name': model_info['name'],
                'rerank_time': round(rerank_time, 3),
                'document_count': len(reranked_docs),
                'top_scores': [doc.metadata.get('rerank_score', 0.0) for doc in reranked_docs[:5]]
            }

            model_results.append(result)

            print(f"   ✅ 完成，耗时 {rerank_time:.3f}s")
            print(f"   Top 5 分数: {result['top_scores']}")

        except Exception as e:
            print(f"   ❌ 测试失败: {str(e)}")
            model_results.append({
                'model_name': model_info['name'],
                'error': str(e)
            })

    return model_results


def interactive_test():
    """交互式测试模式"""
    print(f"\n🎮 交互式重排器测试")
    print("=" * 60)
    print("输入查询进行实时测试，输入 'quit' 退出")

    test_service = RerankerTestService()
    test_service.initialize_retrievers()

    while True:
        try:
            query = input("\n🔍 请输入查询: ").strip()

            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 退出交互式测试")
                break

            if not query:
                print("❌ 请输入有效的查询")
                continue

            # 执行对比测试
            result = test_service.compare_search_results(query)

            # 询问是否查看详细结果
            show_details = input("是否查看详细结果? (y/n): ").strip().lower()
            if show_details in ['y', 'yes']:
                print(f"\n📋 详细结果:")
                print(json.dumps(result, ensure_ascii=False, indent=2))

        except KeyboardInterrupt:
            print("\n👋 退出交互式测试")
            break
        except Exception as e:
            print(f"❌ 测试出错: {str(e)}")


def benchmark_performance():
    """性能基准测试"""
    print(f"\n⚡ 重排器性能基准测试")
    print("=" * 60)

    test_service = RerankerTestService()
    test_service.initialize_retrievers()

    # 准备不同长度的文档集合进行测试
    test_cases = [
        {"name": "小规模", "doc_count": 5, "iterations": 10},
        {"name": "中等规模", "doc_count": 10, "iterations": 5},
        {"name": "大规模", "doc_count": 20, "iterations": 3}
    ]

    query = "心肌梗死的症状和治疗方法"

    # 获取足够的候选文档
    all_docs = test_service.retriever_without_rerank.invoke(query)

    benchmark_results = []

    for test_case in test_cases:
        print(f"\n📊 {test_case['name']}测试 ({test_case['doc_count']} 文档, {test_case['iterations']} 次迭代)")

        docs_subset = all_docs[:test_case['doc_count']]
        times = []

        for i in range(test_case['iterations']):
            start_time = time.time()
            test_service.standalone_reranker.rerank(query, docs_subset, top_n=test_case['doc_count'])
            end_time = time.time()

            times.append(end_time - start_time)
            print(f"   迭代 {i+1}: {times[-1]:.3f}s")

        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)

        result = {
            'test_case': test_case['name'],
            'document_count': test_case['doc_count'],
            'iterations': test_case['iterations'],
            'avg_time': round(avg_time, 3),
            'min_time': round(min_time, 3),
            'max_time': round(max_time, 3),
            'throughput': round(test_case['doc_count'] / avg_time, 1)  # 文档/秒
        }

        benchmark_results.append(result)

        print(f"   平均耗时: {avg_time:.3f}s")
        print(f"   处理速度: {result['throughput']} 文档/秒")

    return benchmark_results


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='重排器功能测试脚本')
    parser.add_argument('--mode', choices=['full', 'interactive', 'benchmark', 'models'],
                       default='full', help='测试模式')
    parser.add_argument('--query', type=str, help='单个查询测试')

    args = parser.parse_args()

    if args.mode == 'full':
        main()
    elif args.mode == 'interactive':
        interactive_test()
    elif args.mode == 'benchmark':
        results = benchmark_performance()
        print(f"\n📋 性能基准测试完成")
        for result in results:
            print(f"   {result['test_case']}: {result['avg_time']}s 平均, {result['throughput']} 文档/秒")
    elif args.mode == 'models':
        results = test_reranker_models()
        print(f"\n📋 模型对比测试完成")
        for result in results:
            if 'error' not in result:
                print(f"   {result['model_name']}: {result['rerank_time']}s")

    if args.query:
        print(f"\n🔍 单查询测试: {args.query}")
        test_service = RerankerTestService()
        test_service.initialize_retrievers()
        result = test_service.compare_search_results(args.query)
        print("✅ 测试完成")
